const { app } = require('@azure/functions')
const {
  BlobServiceClient,
  generateBlobSASQueryParameters,
  BlobSASPermissions,
  StorageSharedKeyCredential,
} = require('@azure/storage-blob')
const { encryptBuffer } = require('../services/encryptionService')
const {
  saveLabReportMetadata,
  updateLabReportMetadata,
} = require('../services/cosmosService')
const { v4: uuidv4 } = require('uuid')
const axios = require('axios')
const { getLabReportMetadata } = require('../services/cosmosService')
const { decryptBuffer } = require('../services/encryptionService')
const { fileTypeFromBuffer } = require('file-type')
const labTestService = require('../services/patient-lab-test-service')
const { LabTestStatus } = require('../common/constant')
const FormData = require('form-data')

// OCR Service Configuration
const OCR_SERVICE_URL =
  'http://ocrcontainergroup-v1.eastus.azurecontainer.io:8000/ocr/'

// Function to send document to OCR service
async function processDocumentWithOCR(buffer, fileName, context) {
  try {
    context.log('Starting OCR processing for file:', fileName)

    const formData = new FormData()
    formData.append('file', buffer, {
      filename: fileName,
      contentType: 'application/pdf',
    })

    const response = await axios.post(OCR_SERVICE_URL, formData, {
      headers: {
        ...formData.getHeaders(),
        Accept: '*/*',
        'Accept-Language': 'en-GB,en-US;q=0.9,en;q=0.8',
        Connection: 'keep-alive',
        'User-Agent':
          'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
      },
      timeout: 120000, // 2 minutes timeout for OCR processing
    })

    context.log('OCR processing completed successfully')
    return response.data
  } catch (error) {
    context.log.error('OCR processing failed:', error.message)
    throw error
  }
}

// Function to match OCR results with lab tests and update them
async function updateLabTestsWithOCRResults(labTestId, ocrData, context) {
  try {
    context.log('Updating lab tests with OCR results for labTestId:', labTestId)

    const labTest = await labTestService.getLabTestById(labTestId)
    if (!labTest || labTest.length === 0) {
      context.log.error('Lab test not found:', labTestId)
      return false
    }

    const labTestRecord = labTest[0]
    const ocrTestResults = ocrData['Test Results'] || []

    // Update lab tests with OCR results
    labTestRecord.labTests = labTestRecord.labTests.map((test) => {
      // Try to find matching test in OCR results
      const matchingOCRTest = ocrTestResults.find((ocrTest) => {
        const testName = test.testName?.toLowerCase() || ''
        const ocrTestName = ocrTest['Test Name']?.toLowerCase() || ''

        // Simple matching logic - can be enhanced based on requirements
        return (
          testName.includes(ocrTestName) ||
          ocrTestName.includes(testName) ||
          testName === ocrTestName
        )
      })

      if (matchingOCRTest) {
        context.log(
          `Matched test: ${test.testName} with OCR result: ${matchingOCRTest['Test Name']}`,
        )
        return {
          ...test,
          results: matchingOCRTest.Value || test.results,
          reference: matchingOCRTest['Reference Interval'] || test.reference,
          status: LabTestStatus.READY,
        }
      }

      return test
    })

    // Update the lab test record
    await labTestService.updateLabTest(labTestRecord)
    context.log('Lab test updated successfully with OCR results')
    return true
  } catch (error) {
    context.log.error(
      'Failed to update lab tests with OCR results:',
      error.message,
    )
    return false
  }
}

app.http('lab-report-upload', {
  methods: ['POST'],
  authLevel: 'function',
  route: 'lab-report/upload',
  handler: async (req, context) => {
    try {
      context.log('Environment check:', {
        hasStorage: !!process.env.AzureWebJobsStorage,
        hasAccountName: !!process.env.AZURE_STORAGE_ACCOUNT_NAME,
        hasAccountKey: !!process.env.AZURE_STORAGE_ACCOUNT_KEY,
        nodeVersion: process.version,
        platform: process.platform,
      })

      const formData = await req.formData()
      const files = formData.getAll('files')
      const patientId = formData.get('patientId')
      const labTestId = formData.get('labTestId')

      if (!files || files.length === 0 || !patientId || !labTestId) {
        return {
          status: 400,
          jsonBody: { error: 'Missing files, patientId, or labTestId' },
        }
      }

      const uploadedMetadata = []

      for (const file of files) {
        if (!(file instanceof File) && !(file instanceof Blob)) {
          return {
            status: 400,
            jsonBody: { error: 'Invalid file object received' },
          }
        }

        if (!file.size || file.size === 0) {
          return {
            status: 400,
            jsonBody: { error: 'Empty file not allowed' },
          }
        }
        async function safeArrayBuffer(file, retries = 3) {
          for (let i = 0; i < retries; i++) {
            try {
              context.log(`ArrayBuffer attempt ${i + 1}`)

              const arrayBuffer = await file.arrayBuffer()

              if (!arrayBuffer) {
                throw new Error('ArrayBuffer is null or undefined')
              }

              if (arrayBuffer.byteLength === 0) {
                throw new Error('ArrayBuffer is empty')
              }

              context.log(
                `ArrayBuffer success: ${arrayBuffer.byteLength} bytes`,
              )
              return arrayBuffer
            } catch (error) {
              context.log(`ArrayBuffer attempt ${i + 1} failed:`, error.message)

              if (i === retries - 1) {
                throw new Error(
                  `Failed to read file after ${retries} attempts: ${error.message}`,
                )
              }

              await new Promise((resolve) => setTimeout(resolve, 100 * (i + 1)))
            }
          }
        }

        async function fallbackFileRead(file) {
          context.log('Using fallback file reading method')

          const chunks = []
          const reader = file.stream().getReader()

          try {
            while (true) {
              const { done, value } = await reader.read()
              if (done) break
              chunks.push(value)
            }

            const totalLength = chunks.reduce(
              (acc, chunk) => acc + chunk.length,
              0,
            )
            const buffer = Buffer.concat(
              chunks.map((chunk) => Buffer.from(chunk)),
              totalLength,
            )

            context.log(`Fallback read success: ${buffer.length} bytes`)
            return buffer
          } catch (streamError) {
            throw new Error(
              `Fallback file reading failed: ${streamError.message}`,
            )
          } finally {
            reader.releaseLock()
          }
        }
        let buffer
        try {
          const arrayBuffer = await safeArrayBuffer(file)
          buffer = Buffer.from(arrayBuffer)
        } catch (arrayBufferError) {
          context.log(
            'ArrayBuffer method failed, trying fallback:',
            arrayBufferError.message,
          )
          buffer = await fallbackFileRead(file)
        }

        if (!buffer || buffer.length === 0) {
          return {
            status: 400,
            jsonBody: { error: 'File buffer is empty or invalid' },
          }
        }

        // Encrypt the buffer
        const { encryptedData, encryptionKey, iv } = await encryptBuffer(buffer)
        const fileId = uuidv4()
        const blobName = `patients/${patientId}/labtest/${labTestId}/${fileId}-${file.name}`

        // Azure Blob Storage setup
        const connectionString = process.env.AzureWebJobsStorage || ''
        const containerName = 'lab-reports'

        const blobServiceClient =
          BlobServiceClient.fromConnectionString(connectionString)
        const containerClient =
          blobServiceClient.getContainerClient(containerName)
        await containerClient.createIfNotExists()

        // Generate SAS URL for upload
        const sharedKeyCredential = new StorageSharedKeyCredential(
          process.env.AZURE_STORAGE_ACCOUNT_NAME,
          process.env.AZURE_STORAGE_ACCOUNT_KEY,
        )

        const sasToken = generateBlobSASQueryParameters(
          {
            containerName,
            blobName,
            permissions: BlobSASPermissions.parse('cw'),
            expiresOn: new Date(Date.now() + 10 * 60 * 1000),
          },
          sharedKeyCredential,
        ).toString()

        const uploadUrl = `https://${process.env.AZURE_STORAGE_ACCOUNT_NAME}.blob.core.windows.net/${containerName}/${blobName}?${sasToken}`

        await axios.put(uploadUrl, encryptedData, {
          headers: {
            'x-ms-blob-type': 'BlockBlob',
            'Content-Length': encryptedData.length,
          },
          timeout: 30000,
        })

        const metadata = await saveLabReportMetadata({
          id: fileId,
          fileName: file.name,
          fileSize: file.size,
          blobPath: blobName,
          encryptionKey,
          iv,
          patientId,
          labTestId,
          ocrStatus: 'pending',
          detectedLanguage: null,
          ocrData: null,
          fileType: file.type,
          uploadedAt: new Date().toISOString(),
        })

        uploadedMetadata.push(metadata)
      }

      const labTest = await labTestService.getLabTestById(labTestId)
      if (labTest && labTest.length > 0) {
        labTest[0].labTests = labTest[0].labTests.map((test) => {
          return {
            ...test,
            status: LabTestStatus.UPLOADED,
            fileMetadata: uploadedMetadata.filter(
              (meta) => meta.labTestId === test.id,
            ),
          }
        })
        await labTestService.updateLabTest(labTest[0])
      }

      context.log('Upload completed successfully')

      return {
        status: 200,
        jsonBody: {
          message: 'Upload successful',
          metadata: uploadedMetadata,
        },
      }
    } catch (error) {
      context.log.error('Upload handler error:', {
        message: error.message,
        stack: error.stack,
        name: error.name,
      })

      return {
        status: 500,
        jsonBody: {
          error: 'Internal server error',
          detail: error.message,
          timestamp: new Date().toISOString(),
        },
      }
    }
  },
})
app.http('lab-report-decrypt', {
  methods: ['GET'],
  authLevel: 'function',
  route: 'lab-report/preview',
  handler: async (req, context) => {
    const docId = req.query.get('docId')
    const metadata = await getLabReportMetadata(docId)

    const sharedKeyCredential = new StorageSharedKeyCredential(
      process.env.AZURE_STORAGE_ACCOUNT_NAME,
      process.env.AZURE_STORAGE_ACCOUNT_KEY,
    )

    const sasToken = generateBlobSASQueryParameters(
      {
        containerName: 'lab-reports',
        blobName: metadata.blobPath,
        permissions: BlobSASPermissions.parse('r'),
        expiresOn: new Date(Date.now() + 5 * 60 * 1000),
      },
      sharedKeyCredential,
    ).toString()

    const downloadUrl = `https://${process.env.AZURE_STORAGE_ACCOUNT_NAME}.blob.core.windows.net/lab-reports/${metadata.blobPath}?${sasToken}`
    const blobRes = await axios.get(downloadUrl, {
      responseType: 'arraybuffer',
    })

    const decryptedBuffer = await decryptBuffer(
      Buffer.from(blobRes.data),
      metadata.encryptionKey,
      metadata.iv,
    )

    const fileType = await fileTypeFromBuffer(decryptedBuffer)

    return {
      status: 200,
      headers: { 'Content-Type': fileType.mime },
      body: decryptedBuffer,
    }
  },
})
